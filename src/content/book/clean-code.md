---
title: "代码整洁之道"
author: "罗伯特·C·马丁"
isbn: "9787115216878"
status: "reading"
startDate: "2025-01-15T00:00:00Z"
tags: ["编程", "软件工程", "代码质量"]
publisher: "人民邮电出版社"
pages: 388
---

# 代码整洁之道

## 基本信息
- **原书名**: Clean Code: A Handbook of Agile Software Craftsmanship
- **作者**: 罗伯特·C·马丁 (<PERSON>)
- **译者**: 韩磊
- **出版社**: 人民邮电出版社
- **开始阅读**: 2025年1月15日
- **当前进度**: 第3章 (约25%)

## 当前阅读笔记

### 第1章：整洁代码
- 整洁代码的重要性
- 不同大师对整洁代码的定义
- **关键观点**: "代码质量与其整洁度成正比"

### 第2章：有意义的命名
- 名副其实的命名
- 避免误导性命名
- 做有意义的区分
- **实践**: 开始在项目中应用更具描述性的变量名

### 第3章：函数 (正在阅读)
- 函数应该短小
- 只做一件事
- 每个函数一个抽象层级

## 阅读感想 (持续更新)

作为一名开发者，这本书让我重新审视自己的编码习惯。特别是命名这一章，让我意识到之前很多变量名都过于简略，缺乏表达力。

## 待深入思考的问题
- 如何在团队中推广整洁代码的理念？
- 如何平衡代码整洁度和开发效率？
- 重构遗留代码的最佳实践是什么？

## 计划应用
- [ ] 重构当前项目中的函数命名
- [ ] 建立团队代码审查标准
- [ ] 整理个人编码规范文档
