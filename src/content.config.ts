import { defineCollection, z } from "astro:content";
import { glob } from "astro/loaders";

function removeDupsAndLowerCase(array: string[]) {
	return [...new Set(array.map((str) => str.toLowerCase()))];
}

const titleSchema = z.string().max(60);

const baseSchema = z.object({
	title: titleSchema,
});

const post = defineCollection({
	loader: glob({ base: "./src/content/post", pattern: "**/*.{md,mdx}" }),
	schema: ({ image }) =>
		baseSchema.extend({
			description: z.string(),
			coverImage: z
				.object({
					alt: z.string(),
					src: image(),
				})
				.optional(),
			draft: z.boolean().default(false),
			ogImage: z.string().optional(),
			tags: z.array(z.string()).default([]).transform(removeDupsAndLowerCase),
			publishDate: z
				.string()
				.or(z.date())
				.transform((val) => new Date(val)),
			updatedDate: z
				.string()
				.optional()
				.transform((str) => (str ? new Date(str) : undefined)),
			pinned: z.boolean().default(false),
		}),
});

const note = defineCollection({
	loader: glob({ base: "./src/content/note", pattern: "**/*.{md,mdx}" }),
	schema: baseSchema.extend({
		description: z.string().optional(),
		publishDate: z
			.string()
			.datetime({ offset: true }) // Ensures ISO 8601 format with offsets allowed (e.g. "2024-01-01T00:00:00Z" and "2024-01-01T00:00:00+02:00")
			.transform((val) => new Date(val)),
	}),
});

const tag = defineCollection({
	loader: glob({ base: "./src/content/tag", pattern: "**/*.{md,mdx}" }),
	schema: z.object({
		title: titleSchema.optional(),
		description: z.string().optional(),
	}),
});

const book = defineCollection({
	loader: glob({ base: "./src/content/book", pattern: "**/*.{md,mdx}" }),
	schema: baseSchema.extend({
		author: z.string(),
		isbn: z.string().optional(),
		rating: z.number().min(1).max(5).optional(),
		status: z.enum(["reading", "completed", "abandoned"]).default("completed"),
		startDate: z
			.string()
			.or(z.date())
			.transform((val) => new Date(val))
			.optional(),
		finishDate: z
			.string()
			.or(z.date())
			.transform((val) => new Date(val))
			.optional(),
		tags: z.array(z.string()).default([]).transform(removeDupsAndLowerCase),
		coverImage: z.string().optional(),
		publisher: z.string().optional(),
		pages: z.number().optional(),
	}),
});

export const collections = { post, note, tag, book };
