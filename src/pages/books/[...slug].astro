---
import { getCollection, type CollectionEntry, render } from "astro:content";
import PageLayout from "@/layouts/Base.astro";

export async function getStaticPaths() {
	const books = await getCollection("book");
	return books.map((book) => ({
		params: { slug: book.id },
		props: { book },
	}));
}

interface Props {
	book: CollectionEntry<"book">;
}

const { book } = Astro.props;
const { Content } = await render(book);

// 格式化日期
function formatDate(date: Date | undefined) {
	if (!date) return "";
	return date.toLocaleDateString("zh-CN", {
		year: "numeric",
		month: "long",
		day: "numeric",
	});
}

// 生成星级评分
function generateStars(rating: number | undefined) {
	if (!rating) return "";
	return "⭐".repeat(rating);
}

const meta = {
	title: `《${book.data.title}》- 读书笔记`,
	description: `${book.data.author} 著作《${book.data.title}》的读书笔记和感想`,
};
---

<PageLayout meta={meta}>
	<div class="prose prose-sm prose-cactus max-w-none">
		<!-- 返回链接 -->
		<div class="mb-6">
			<a
				href="/books/"
				class="text-sm text-gray-600 hover:text-gray-900 hover:underline"
			>
				← 返回读书记录
			</a>
		</div>

		<!-- 书籍信息卡片 -->
		<div class="mb-8 p-6 bg-gray-50 rounded-lg border">
			<div class="flex justify-between items-start mb-4">
				<div>
					<h1 class="text-2xl font-bold text-gray-900 mb-2">
						《{book.data.title}》
					</h1>
					<p class="text-lg text-gray-700">
						作者: {book.data.author}
					</p>
				</div>
				<div class="text-right">
					{
						book.data.status === "reading" && (
							<span class="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
								📖 正在阅读
							</span>
						)
					}
					{
						book.data.status === "completed" && (
							<span class="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
								✅ 已完成
							</span>
						)
					}
					{
						book.data.status === "abandoned" && (
							<span class="inline-block px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
								❌ 已放弃
							</span>
						)
					}
				</div>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
				{
					book.data.startDate && (
						<div>
							<span class="font-medium text-gray-600">
								开始阅读:
							</span>
							<span class="ml-2">
								{formatDate(book.data.startDate)}
							</span>
						</div>
					)
				}
				{
					book.data.finishDate && (
						<div>
							<span class="font-medium text-gray-600">
								完成阅读:
							</span>
							<span class="ml-2">
								{formatDate(book.data.finishDate)}
							</span>
						</div>
					)
				}
				{
					book.data.publisher && (
						<div>
							<span class="font-medium text-gray-600">
								出版社:
							</span>
							<span class="ml-2">{book.data.publisher}</span>
						</div>
					)
				}
				{
					book.data.pages && (
						<div>
							<span class="font-medium text-gray-600">页数:</span>
							<span class="ml-2">{book.data.pages} 页</span>
						</div>
					)
				}
				{
					book.data.isbn && (
						<div>
							<span class="font-medium text-gray-600">ISBN:</span>
							<span class="ml-2">{book.data.isbn}</span>
						</div>
					)
				}
				{
					book.data.rating && (
						<div>
							<span class="font-medium text-gray-600">评分:</span>
							<span class="ml-2 text-yellow-500">
								{generateStars(book.data.rating)}
							</span>
						</div>
					)
				}
			</div>

			{
				book.data.tags.length > 0 && (
					<div class="mt-4">
						<span class="font-medium text-gray-600 mr-2">
							标签:
						</span>
						<div class="inline-flex flex-wrap gap-1">
							{book.data.tags.map((tag) => (
								<span class="text-xs bg-gray-200 text-gray-800 px-2 py-1 rounded">
									{tag}
								</span>
							))}
						</div>
					</div>
				)
			}
		</div>

		<!-- 读书笔记内容 -->
		<article class="prose prose-sm prose-cactus max-w-none">
			<Content />
		</article>

		<!-- 返回链接 -->
		<div class="mt-12 pt-6 border-t border-gray-200">
			<a
				href="/books/"
				class="text-sm text-gray-600 hover:text-gray-900 hover:underline"
			>
				← 返回读书记录
			</a>
		</div>
	</div>
</PageLayout>
