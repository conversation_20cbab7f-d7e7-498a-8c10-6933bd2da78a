---
import PageLayout from "@/layouts/Base.astro";

const meta = {
	description: "我的读书记录",
	title: "Books",
};
---

<PageLayout meta={meta}>
	<div class="prose prose-sm prose-cactus max-w-none">
		<h1 class="title mb-6">📚 读书记录</h1>
		
		<p class="mb-8 text-gray-600">
			记录我读过的书籍，包括阅读时间、评分和简短感想。
		</p>

		<!-- 2025年 -->
		<h2 class="text-xl font-bold mb-4 border-b border-gray-300 pb-2">2025年</h2>
		
		<div class="mb-8">
			<h3 class="text-lg font-semibold mb-3">📖 正在阅读</h3>
			<div class="space-y-4">
				<!-- 示例：正在阅读的书 -->
				<div class="border-l-4 border-blue-500 pl-4 bg-blue-50 p-3 rounded-r">
					<div class="flex justify-between items-start mb-2">
						<h4 class="font-medium text-gray-900">《书名》</h4>
						<span class="text-sm text-gray-500">开始时间: 2025-01-01</span>
					</div>
					<p class="text-sm text-gray-600 mb-2">作者: 作者名</p>
					<p class="text-sm text-gray-700">当前进度和感想...</p>
				</div>
			</div>
		</div>

		<div class="mb-8">
			<h3 class="text-lg font-semibold mb-3">✅ 已完成</h3>
			<div class="space-y-4">
				<!-- 示例：已读完的书 -->
				<div class="border-l-4 border-green-500 pl-4 bg-green-50 p-3 rounded-r">
					<div class="flex justify-between items-start mb-2">
						<h4 class="font-medium text-gray-900">《示例书名》</h4>
						<div class="text-right">
							<div class="text-sm text-gray-500">2025-01-15 完成</div>
							<div class="text-yellow-500">⭐⭐⭐⭐⭐</div>
						</div>
					</div>
					<p class="text-sm text-gray-600 mb-2">作者: 示例作者</p>
					<p class="text-sm text-gray-700">这本书给我的感想和收获...</p>
				</div>
			</div>
		</div>

		<!-- 2024年 -->
		<h2 class="text-xl font-bold mb-4 border-b border-gray-300 pb-2">2024年</h2>
		
		<div class="mb-8">
			<h3 class="text-lg font-semibold mb-3">✅ 已完成</h3>
			<div class="space-y-4">
				<!-- 2024年的书籍记录 -->
				<div class="border-l-4 border-green-500 pl-4 bg-green-50 p-3 rounded-r">
					<div class="flex justify-between items-start mb-2">
						<h4 class="font-medium text-gray-900">《书名》</h4>
						<div class="text-right">
							<div class="text-sm text-gray-500">完成日期</div>
							<div class="text-yellow-500">⭐⭐⭐⭐⭐</div>
						</div>
					</div>
					<p class="text-sm text-gray-600 mb-2">作者: 作者名</p>
					<p class="text-sm text-gray-700">读书感想...</p>
				</div>
			</div>
		</div>

		<!-- 统计信息 -->
		<div class="mt-12 p-4 bg-gray-100 rounded-lg">
			<h3 class="text-lg font-semibold mb-3">📊 阅读统计</h3>
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
				<div>
					<div class="text-2xl font-bold text-blue-600">0</div>
					<div class="text-sm text-gray-600">2025年已读</div>
				</div>
				<div>
					<div class="text-2xl font-bold text-green-600">0</div>
					<div class="text-sm text-gray-600">2024年已读</div>
				</div>
				<div>
					<div class="text-2xl font-bold text-purple-600">0</div>
					<div class="text-sm text-gray-600">总计</div>
				</div>
				<div>
					<div class="text-2xl font-bold text-orange-600">0</div>
					<div class="text-sm text-gray-600">正在阅读</div>
				</div>
			</div>
		</div>

		<!-- 阅读目标 -->
		<div class="mt-8 p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
			<h3 class="text-lg font-semibold mb-2">🎯 2025年阅读目标</h3>
			<p class="text-gray-700">今年计划阅读 <strong>12</strong> 本书</p>
			<div class="mt-2 bg-gray-200 rounded-full h-2">
				<div class="bg-yellow-400 h-2 rounded-full" style="width: 0%"></div>
			</div>
			<p class="text-sm text-gray-600 mt-1">进度: 0/12 (0%)</p>
		</div>

		<!-- 使用说明 -->
		<div class="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
			<h3 class="text-lg font-semibold mb-2">📝 使用说明</h3>
			<ul class="text-sm text-gray-700 space-y-1">
				<li>• <strong>正在阅读</strong>: 记录当前正在读的书籍和进度</li>
				<li>• <strong>已完成</strong>: 记录读完的书籍，包括评分和感想</li>
				<li>• <strong>评分系统</strong>: ⭐⭐⭐⭐⭐ (1-5星)</li>
				<li>• <strong>颜色说明</strong>: 蓝色边框=正在读，绿色边框=已完成</li>
			</ul>
		</div>
	</div>
</PageLayout>
