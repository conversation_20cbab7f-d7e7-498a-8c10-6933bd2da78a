# 读书记录系统使用指南

这个目录用于管理你的读书记录。每本书对应一个 Markdown 文件，包含书籍信息和你的读书笔记。

## 文件命名规范

建议使用以下命名格式：
- 英文书名：`book-title.md`
- 中文书名：使用拼音或英文翻译，如 `thinking-fast-and-slow.md`
- 避免使用特殊字符和空格

## Frontmatter 字段说明

每个书籍文件都需要包含以下 frontmatter 信息：

### 必填字段
- `title`: 书名
- `author`: 作者名

### 可选字段
- `isbn`: ISBN 号码
- `rating`: 评分 (1-5)
- `status`: 阅读状态
  - `reading`: 正在阅读
  - `completed`: 已完成
  - `abandoned`: 已放弃
- `startDate`: 开始阅读日期 (ISO 8601 格式)
- `finishDate`: 完成阅读日期 (ISO 8601 格式)
- `tags`: 标签数组，如 `["心理学", "认知科学"]`
- `publisher`: 出版社
- `pages`: 页数
- `coverImage`: 封面图片 URL

## 示例文件

参考 `_template.md` 文件作为模板，或查看现有的示例文件。

## 日期格式

使用 ISO 8601 格式：`2025-01-01T00:00:00Z`

## 标签建议

常用标签分类：
- **学科**: 心理学、哲学、历史、科学、经济学
- **类型**: 小说、传记、技术、管理、自我提升
- **主题**: 思维、决策、创新、领导力、沟通

## 页面功能

- 访问 `/books/` 查看所有书籍列表
- 点击书名查看详细读书笔记
- 自动按年份分组显示
- 实时统计阅读数据
- 阅读进度跟踪
