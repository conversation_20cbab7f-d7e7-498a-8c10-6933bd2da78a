# 文章导航功能

## 功能概述

文章导航功能为博客文章页面添加了上一篇/下一篇文章的导航链接，让读者可以轻松地在文章之间浏览，提升阅读体验的连续性。

## 主要特性

### 🎨 视觉设计
- **渐变背景**：优雅的渐变背景效果
- **悬停动画**：鼠标悬停时的微妙上移和阴影效果
- **图标动画**：箭头图标的平移动画反馈
- **圆形图标容器**：现代化的圆形图标设计

### 📱 响应式设计
- **移动端优化**：在小屏幕上垂直排列，大屏幕上水平排列
- **深色模式支持**：自动适配深色主题
- **高对比度模式**：为视觉障碍用户提供更好的可访问性

### ⌨️ 键盘导航
- **左箭头键**：导航到上一篇文章
- **右箭头键**：导航到下一篇文章
- **智能检测**：不会在输入框中触发导航
- **修饰键过滤**：避免与其他快捷键冲突

### ♿ 可访问性
- **ARIA 标签**：正确的语义化标记
- **减少动画**：尊重用户的 `prefers-reduced-motion` 设置
- **键盘友好**：完整的键盘导航支持
- **屏幕阅读器友好**：清晰的文本标识

## 技术实现

### 文件结构
```
src/components/blog/PostNavigation.astro  # 导航组件
src/data/post.ts                         # 相邻文章获取逻辑
src/layouts/BlogPost.astro               # 集成到博客布局
```

### 核心功能

#### 1. 相邻文章获取
```typescript
export function getAdjacentPosts(
	currentPost: CollectionEntry<"post">,
	allPosts: CollectionEntry<"post">[]
): { prevPost: CollectionEntry<"post"> | null; nextPost: CollectionEntry<"post"> | null }
```

- 按发布日期排序所有文章
- 找到当前文章的位置
- 返回前一篇（更新的）和后一篇（更旧的）文章

#### 2. 导航组件
- 使用 Astro 组件构建
- 集成 Astro Icon 图标系统
- 响应式 Tailwind CSS 样式
- 渐进增强的 JavaScript 功能

#### 3. 键盘导航
- 监听全局键盘事件
- 智能过滤输入场景
- 使用 `click()` 方法触发导航

## 使用方法

### 自动集成
导航功能已经自动集成到所有博客文章页面中，无需额外配置。

### 用户体验
1. **视觉导航**：在文章底部看到上一篇/下一篇链接
2. **鼠标交互**：悬停时的视觉反馈和动画效果
3. **键盘导航**：使用左右箭头键快速切换文章
4. **响应式体验**：在不同设备上都有良好的显示效果

## 自定义选项

### 样式自定义
可以通过修改组件中的 Tailwind 类来自定义外观：

```astro
<!-- 修改背景渐变 -->
class="bg-gradient-to-r from-your-color/80 to-your-color/40"

<!-- 修改悬停效果 -->
class="hover:border-your-accent hover:from-your-accent/5"
```

### 禁用键盘导航
如果需要禁用键盘导航，可以移除组件中的 `<script>` 部分。

### 修改导航文本
可以修改组件中的文本标签：
```astro
<div class="text-xs font-semibold uppercase tracking-wider">
  Previous Post  <!-- 修改为英文 -->
</div>
```

## 排序逻辑

文章按发布日期排序（最新的在前）：
- **上一篇**：发布日期更新的文章
- **下一篇**：发布日期更旧的文章

这确保了读者可以按时间顺序浏览文章内容。

## 性能考虑

- **服务端渲染**：导航链接在构建时生成，无运行时开销
- **最小 JavaScript**：只有键盘导航需要少量 JavaScript
- **CSS 优化**：使用 GPU 加速的 CSS 属性
- **懒加载友好**：不会影响页面的初始加载性能

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 故障排除

### 导航不显示
1. 确认有多篇文章存在
2. 检查文章的发布日期设置
3. 确认不是第一篇或最后一篇文章

### 键盘导航不工作
1. 检查浏览器控制台是否有 JavaScript 错误
2. 确认不在输入框中按键
3. 检查是否有其他脚本冲突

### 样式问题
1. 确认 Tailwind CSS 正常加载
2. 检查是否有 CSS 冲突
3. 验证 Astro Icon 组件正常工作

## 未来改进

### 计划中的功能
- [ ] 显示文章发布日期
- [ ] 添加文章摘要预览
- [ ] 支持文章分类导航
- [ ] 添加阅读时间显示

### 可能的增强
- [ ] 触摸手势支持（滑动导航）
- [ ] 预加载相邻文章内容
- [ ] 导航历史记录
- [ ] 自定义导航顺序（如按标签分组）
